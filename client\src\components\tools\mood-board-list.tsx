import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Plus,
  Edit3,
  Trash2,
  Calendar,
  Eye,
  MoreVertical,
  Search,
  Filter,
  Loader2
} from "lucide-react"
import { useLocation } from "wouter"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useMoodboard } from "@/hooks/use-moodboard"
import { useAuth } from "@/hooks/use-auth"
import type { Moodboard } from "@/lib/supabase"

export default function MoodBoardList() {
  const [, setLocation] = useLocation()
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedFilter, setSelectedFilter] = useState("all")
  const { user, isLoading: isAuthLoading } = useAuth()

  // Use the moodboard hook for data management
  const {
    moodboardList,
    isLoadingList,
    listError,
    deleteMoodboard,
    isDeleting,
    refetchList
  } = useMoodboard()

  // Filter moodboards based on search and filter criteria
  const filteredMoodBoards = moodboardList.filter(board => {
    const matchesSearch = board.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (board.description || "").toLowerCase().includes(searchTerm.toLowerCase())

    if (selectedFilter === "all") return matchesSearch
    if (selectedFilter === "public") return matchesSearch && board.is_public
    if (selectedFilter === "private") return matchesSearch && !board.is_public

    return matchesSearch
  })

  const handleCreateNew = async () => {
    // Navigate to editor with 'new' ID - the editor will handle creation
    setLocation(`/dashboard/herramientas/mood-board/editor/new`)
  }

  const handleOpenBoard = (boardId: string) => {
    setLocation(`/dashboard/herramientas/mood-board/editor/${boardId}`)
  }

  const handleDeleteBoard = async (boardId: string) => {
    try {
      await deleteMoodboard(boardId)
      // The hook will automatically update the list via query invalidation
    } catch (error) {
      console.error('Error deleting moodboard:', error)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  // Show loading state while authenticating or loading data
  if (isAuthLoading || isLoadingList) {
    return (
      <div className="container mx-auto p-6 max-w-7xl">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-indigo-600" />
            <p className="text-gray-600">Cargando mood boards...</p>
          </div>
        </div>
      </div>
    )
  }

  // Show error state if there's an error loading data
  if (listError) {
    return (
      <div className="container mx-auto p-6 max-w-7xl">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600 mb-4">Error al cargar los mood boards</p>
            <Button onClick={() => refetchList()} variant="outline">
              Reintentar
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Show authentication required state
  if (!user) {
    return (
      <div className="container mx-auto p-6 max-w-7xl">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-gray-600 mb-4">Debes iniciar sesión para ver tus mood boards</p>
            <Button onClick={() => setLocation('/login')} className="bg-gradient-to-r from-indigo-600 to-purple-600">
              Iniciar Sesión
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 text-transparent bg-clip-text">
              Mis Mood Boards
            </h1>
            <p className="text-gray-600 mt-2 text-lg">
              Gestiona y crea tableros de inspiración visual para tus proyectos
            </p>
          </div>

          <Button
            onClick={handleCreateNew}
            size="lg"
            className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-6 py-3"
          >
            <Plus className="w-5 h-5 mr-2" />
            Crear Nuevo
          </Button>
        </div>

        {/* Search and Filters */}
        <div className="flex items-center gap-4 mb-6">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Buscar mood boards..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="gap-2">
                <Filter className="w-4 h-4" />
                Filtrar
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setSelectedFilter("all")}>
                Todos
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSelectedFilter("public")}>
                Públicos
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSelectedFilter("private")}>
                Privados
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </motion.div>

      {/* Loading State */}
      {isLoadingList ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="text-gray-500 mt-2">Cargando mood boards...</p>
        </div>
      ) : (
        <>
          {/* Mood Boards Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredMoodBoards.map((board, index) => (
          <motion.div
            key={board.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="group hover:shadow-xl transition-all duration-300 cursor-pointer overflow-hidden">
              <div className="relative">
                {/* Use canvas snapshot if available, otherwise show placeholder */}
                {board.canvas_snapshot ? (
                  <img
                    src={board.canvas_snapshot}
                    alt={board.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                ) : (
                  <div className="w-full h-48 bg-gradient-to-br from-indigo-100 to-purple-100 flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-indigo-200 rounded-full flex items-center justify-center mb-2">
                        <Edit3 className="w-8 h-8 text-indigo-600" />
                      </div>
                      <p className="text-indigo-600 font-medium">Mood Board</p>
                    </div>
                  </div>
                )}

                <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                  <Button
                    size="sm"
                    onClick={() => handleOpenBoard(board.id)}
                    className="bg-white text-gray-800 hover:bg-white/90"
                  >
                    <Edit3 className="w-4 h-4 mr-1" />
                    Editar
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    className="bg-white/90 text-gray-800 hover:bg-white"
                  >
                    <Eye className="w-4 h-4 mr-1" />
                    Ver
                  </Button>
                </div>

                {/* Status Badge */}
                <div className="absolute top-3 right-3">
                  <Badge variant={board.is_public ? "default" : "secondary"}>
                    {board.is_public ? "Público" : "Privado"}
                  </Badge>
                </div>
              </div>
              
              <CardContent className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-semibold text-lg line-clamp-1">{board.title}</h3>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="opacity-0 group-hover:opacity-100 transition-opacity"
                        disabled={isDeleting}
                      >
                        <MoreVertical className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => handleOpenBoard(board.id)}>
                        <Edit3 className="w-4 h-4 mr-2" />
                        Editar
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-red-600"
                        onClick={() => handleDeleteBoard(board.id)}
                        disabled={isDeleting}
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        {isDeleting ? "Eliminando..." : "Eliminar"}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                  {board.description || "Sin descripción"}
                </p>

                {/* Tags */}
                <div className="flex flex-wrap gap-1 mb-3">
                  {board.tags.map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>

                {/* Date and Stats */}
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <div className="flex items-center">
                    <Calendar className="w-3 h-3 mr-1" />
                    Actualizado {formatDate(board.updated_at)}
                  </div>
                  {board.view_count > 0 && (
                    <div className="flex items-center">
                      <Eye className="w-3 h-3 mr-1" />
                      {board.view_count} vistas
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
          </div>

          {/* Empty State */}
          {filteredMoodBoards.length === 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <Plus className="w-12 h-12 text-gray-400" />
          </div>
          <h3 className="text-xl font-semibold mb-2">No hay mood boards</h3>
          <p className="text-gray-600 mb-4">
            {searchTerm ? "No se encontraron resultados para tu búsqueda" : "Crea tu primer mood board para empezar"}
          </p>
          <Button onClick={handleCreateNew} className="bg-gradient-to-r from-indigo-600 to-purple-600">
            <Plus className="w-4 h-4 mr-2" />
            Crear Mood Board
          </Button>
        </motion.div>
          )}
        </>
      )}
    </div>
  )
}
