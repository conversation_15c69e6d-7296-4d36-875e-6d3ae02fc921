import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import {
  Plus,
  Edit3,
  Trash2,
  Calendar,
  Eye,
  MoreVertical,
  Search,
  Filter,
  Loader2,
  History,
  Heart,
  Palette
} from "lucide-react"
import { useLocation } from "wouter"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useMoodboard } from "@/hooks/use-moodboard"
import { useAuth } from "@/hooks/use-auth"
import type { Moodboard } from "@/lib/supabase"

export default function MoodBoardList() {
  const [, setLocation] = useLocation()
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedFilter, setSelectedFilter] = useState("all")
  const [activeTab, setActiveTab] = useState("editor")
  const { user, isLoading: isAuthLoading } = useAuth()

  // Use the moodboard hook for data management
  const {
    moodboardList,
    isLoadingList,
    listError,
    deleteMoodboard,
    isDeleting,
    refetchList
  } = useMoodboard()

  // Filter moodboards based on search and filter criteria
  const filteredMoodBoards = moodboardList.filter(board => {
    const matchesSearch = board.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (board.description || "").toLowerCase().includes(searchTerm.toLowerCase())

    if (selectedFilter === "all") return matchesSearch
    if (selectedFilter === "public") return matchesSearch && board.is_public
    if (selectedFilter === "private") return matchesSearch && !board.is_public

    return matchesSearch
  })

  const handleCreateNew = async () => {
    // Navigate to editor with 'new' ID - the editor will handle creation
    setLocation(`/dashboard/herramientas/mood-board/editor/new`)
  }

  const handleOpenBoard = (boardId: string) => {
    setLocation(`/dashboard/herramientas/mood-board/editor/${boardId}`)
  }

  const handleDeleteBoard = async (boardId: string) => {
    try {
      await deleteMoodboard(boardId)
      // The hook will automatically update the list via query invalidation
    } catch (error) {
      console.error('Error deleting moodboard:', error)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  // Show loading state while authenticating or loading data
  if (isAuthLoading || isLoadingList) {
    return (
      <div className="container mx-auto p-6 max-w-7xl">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-indigo-600" />
            <p className="text-gray-600">Cargando mood boards...</p>
          </div>
        </div>
      </div>
    )
  }

  // Show error state if there's an error loading data
  if (listError) {
    return (
      <div className="container mx-auto p-6 max-w-7xl">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-600 mb-4">Error al cargar los mood boards</p>
            <Button onClick={() => refetchList()} variant="outline">
              Reintentar
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Show authentication required state
  if (!user) {
    return (
      <div className="container mx-auto p-6 max-w-7xl">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-gray-600 mb-4">Debes iniciar sesión para ver tus mood boards</p>
            <Button onClick={() => setLocation('/login')} className="bg-gradient-to-r from-indigo-600 to-purple-600">
              Iniciar Sesión
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 text-transparent bg-clip-text">
              Mood Board Editor
            </h1>
            <p className="text-gray-600 mt-2 text-lg">
              Crea y gestiona tableros de inspiración visual para tus proyectos
            </p>
          </div>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="editor" className="flex items-center gap-2">
              <Palette className="h-4 w-4" />
              Editor
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2">
              <History className="h-4 w-4" />
              Historial ({moodboardList.length})
            </TabsTrigger>
          </TabsList>

          {/* Editor Tab Content */}
          <TabsContent value="editor" className="space-y-6">
            <Card className="border-2 border-gray-200 shadow-sm">
              <CardHeader className="border-b border-gray-100 bg-gray-50 rounded-t-lg">
                <CardTitle className="text-indigo-600 flex items-center gap-2">
                  <Palette className="h-5 w-5 text-indigo-600" />
                  Crear Nuevo Mood Board
                </CardTitle>
                <p className="text-gray-600 mt-2">
                  Comienza un nuevo proyecto de mood board para organizar tus ideas visuales
                </p>
              </CardHeader>
              <CardContent className="space-y-4 pt-6">
                <div className="text-center py-12">
                  <Palette className="h-16 w-16 text-indigo-300 mx-auto mb-4" />
                  <h3 className="font-medium text-gray-700 mb-2">
                    ¡Comienza tu creatividad!
                  </h3>
                  <p className="text-sm text-gray-500 mb-6">
                    Crea un nuevo mood board para organizar imágenes, colores y elementos visuales
                  </p>
                  <Button
                    onClick={handleCreateNew}
                    size="lg"
                    className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-8 py-3"
                  >
                    <Plus className="w-5 h-5 mr-2" />
                    Crear Nuevo Mood Board
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* History Tab Content */}
          <TabsContent value="history" className="space-y-6">
            {/* Search and Filters for History */}
            <div className="flex items-center gap-4 mb-6">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Buscar mood boards..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="gap-2">
                    <Filter className="w-4 h-4" />
                    Filtrar
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => setSelectedFilter("all")}>
                    Todos
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setSelectedFilter("public")}>
                    Públicos
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setSelectedFilter("private")}>
                    Privados
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Loading State */}
            {isLoadingList ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
                <p className="text-gray-500 mt-2">Cargando mood boards...</p>
              </div>
            ) : filteredMoodBoards.length === 0 ? (
              <Card className="p-8 text-center">
                <History className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <h3 className="font-medium text-gray-700 mb-2">
                  No hay mood boards en tu historial
                </h3>
                <p className="text-sm text-gray-500 mb-4">
                  Los mood boards que crees aparecerán aquí
                </p>
                <Button onClick={() => setActiveTab("editor")}>
                  Crear primer mood board
                </Button>
              </Card>
            ) : (
              /* Mood Boards Grid */
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredMoodBoards.map((board, index) => (
          <motion.div
            key={board.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className="group hover:shadow-xl transition-all duration-300 cursor-pointer overflow-hidden">
              <div className="relative">
                {/* Use canvas snapshot if available, otherwise show placeholder */}
                {board.canvas_snapshot ? (
                  <img
                    src={board.canvas_snapshot}
                    alt={board.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                ) : (
                  <div className="w-full h-48 bg-gradient-to-br from-indigo-100 to-purple-100 flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                    <div className="text-center">
                      <div className="w-16 h-16 bg-indigo-200 rounded-full flex items-center justify-center mb-2">
                        <Edit3 className="w-8 h-8 text-indigo-600" />
                      </div>
                      <p className="text-indigo-600 font-medium">Mood Board</p>
                    </div>
                  </div>
                )}

                <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                  <Button
                    size="sm"
                    onClick={() => handleOpenBoard(board.id)}
                    className="bg-white text-gray-800 hover:bg-white/90"
                  >
                    <Edit3 className="w-4 h-4 mr-1" />
                    Editar
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    className="bg-white/90 text-gray-800 hover:bg-white"
                  >
                    <Eye className="w-4 h-4 mr-1" />
                    Ver
                  </Button>
                </div>

                {/* Status Badge */}
                <div className="absolute top-3 right-3">
                  <Badge variant={board.is_public ? "default" : "secondary"}>
                    {board.is_public ? "Público" : "Privado"}
                  </Badge>
                </div>
              </div>
              
              <CardContent className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-semibold text-lg line-clamp-1">{board.title}</h3>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="opacity-0 group-hover:opacity-100 transition-opacity"
                        disabled={isDeleting}
                      >
                        <MoreVertical className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => handleOpenBoard(board.id)}>
                        <Edit3 className="w-4 h-4 mr-2" />
                        Editar
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-red-600"
                        onClick={() => handleDeleteBoard(board.id)}
                        disabled={isDeleting}
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        {isDeleting ? "Eliminando..." : "Eliminar"}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                  {board.description || "Sin descripción"}
                </p>

                {/* Tags */}
                <div className="flex flex-wrap gap-1 mb-3">
                  {board.tags.map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>

                {/* Date and Stats */}
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <div className="flex items-center">
                    <Calendar className="w-3 h-3 mr-1" />
                    Actualizado {formatDate(board.updated_at)}
                  </div>
                  {board.view_count > 0 && (
                    <div className="flex items-center">
                      <Eye className="w-3 h-3 mr-1" />
                      {board.view_count} vistas
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </motion.div>
    </div>
  )
}
