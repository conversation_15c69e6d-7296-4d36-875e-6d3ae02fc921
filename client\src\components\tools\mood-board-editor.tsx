import { Tldraw, useEditor, TLUiTranslationProvider } from '@tldraw/tldraw'
import '@tldraw/tldraw/tldraw.css'
import { Button } from "@/components/ui/button"
import {
  ArrowLeft,
  Download,
  Share2,
  Save,
  Settings,
  Users,
  Eye,
  EyeOff,
  Edit2,
  Check,
  X,
  Loader2
} from "lucide-react"
import { useState, useEffect, useRef } from "react"
import { useLocation } from "wouter"
import { motion, AnimatePresence } from "framer-motion"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { useToast } from "@/hooks/use-toast"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useMoodboard } from "@/hooks/use-moodboard"
import { useAuth } from "@/hooks/use-auth"

interface MoodBoardEditorProps {
  boardId: string
}

// Variable global para almacenar la función de exportar
let globalExportFunction: (() => void) | null = null

// Componente interno que tiene acceso al editor de tldraw
function TldrawExportHandler({ boardTitle, toast }: { boardTitle: string, toast: any }) {
  const editor = useEditor()

  const handleExport = async () => {
    console.log('Export button clicked!') // Debug

    try {
      if (!editor) {
        console.log('No editor available')
        toast({
          title: "Error",
          description: "Editor no disponible",
          variant: "destructive"
        })
        return
      }

      console.log('Editor available, starting export...')

      // Usar el método más simple de tldraw
      const dataUrl = await editor.exportAs(editor.getCurrentPageShapeIds(), 'png')

      if (!dataUrl) {
        toast({
          title: "Canvas vacío",
          description: "Añade algunos elementos antes de exportar",
          variant: "destructive"
        })
        return
      }

      // Crear enlace de descarga
      const link = document.createElement('a')
      link.href = dataUrl
      link.download = `${boardTitle.replace(/\s+/g, '_')}.png`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: "¡Exportado!",
        description: "Tu mood board se ha descargado exitosamente"
      })

    } catch (error) {
      console.error('Export error:', error)
      toast({
        title: "Error al exportar",
        description: "Hubo un problema al exportar tu mood board",
        variant: "destructive"
      })
    }
  }

  // Exponer la función globalmente
  useEffect(() => {
    globalExportFunction = handleExport
    return () => {
      globalExportFunction = null
    }
  }, [handleExport])

  return null // Este componente no renderiza nada visible
}

export default function MoodBoardEditor({ boardId }: MoodBoardEditorProps) {
  const [, setLocation] = useLocation()
  const { toast } = useToast()
  const { user, isLoading: isAuthLoading } = useAuth()

  // Estados del editor
  const [isToolbarVisible, setIsToolbarVisible] = useState(true)
  const [isEditingTitle, setIsEditingTitle] = useState(false)
  const [tempTitle, setTempTitle] = useState("")
  const [showSettings, setShowSettings] = useState(false)
  const [currentMoodboardId, setCurrentMoodboardId] = useState<string | null>(null)

  // Referencias
  const titleInputRef = useRef<HTMLInputElement>(null)
  const editorRef = useRef<any>(null)

  // Use moodboard hook with auto-save enabled
  const enableAutoSave = true
  const {
    moodboard,
    isLoadingMoodboard,
    moodboardError,
    createMoodboard,
    manualSave,
    scheduleAutoSave,
    lastSaved,
    hasUnsavedChanges,
    isCreating,
    isUpdating
  } = useMoodboard(boardId === 'new' ? undefined : boardId, {
    enableAutoSave,
    autoSaveInterval: 30000 // 30 seconds
  })

  // Debug logging for authentication state (following Visual Complexity Analyzer pattern)
  useEffect(() => {
    console.log('🎨 Mood Board Editor - Auth State:', {
      user,
      isAuthLoading,
      userId: user?.id,
      userEmail: user?.email,
      userName: user?.username,
      boardId
    });
  }, [user, isAuthLoading, boardId]);

  // Initialize component state from loaded moodboard data
  useEffect(() => {
    if (moodboard) {
      setTempTitle(moodboard.title)
      setCurrentMoodboardId(moodboard.id)
    } else if (boardId === 'new') {
      setTempTitle("Nuevo Mood Board")
      setCurrentMoodboardId(null)
    }
  }, [moodboard, boardId])

  const handleBack = () => {
    setLocation("/dashboard/herramientas/mood-board")
  }

  const handleSave = async () => {
    try {
      if (!user) {
        toast({
          title: "Error",
          description: "Debes iniciar sesión para guardar",
          variant: "destructive"
        })
        return
      }

      if (boardId === 'new' || !currentMoodboardId) {
        // Create new moodboard
        const result = await createMoodboard({
          title: tempTitle || "Nuevo Mood Board",
          description: "",
          tldraw_data: {}, // Will be updated with actual data
          tags: [],
          is_public: false,
          is_favorite: false
        })

        if (result?.data?.id) {
          setCurrentMoodboardId(result.data.id)
          // Update URL to reflect the new ID
          setLocation(`/dashboard/herramientas/mood-board/editor/${result.data.id}`)

          // Show success message
          toast({
            title: "Mood Board creado",
            description: "Tu mood board ha sido creado exitosamente",
          })
        }
      } else {
        // Update existing moodboard with current editor state
        const updateData: any = {
          title: tempTitle,
        }

        // Capture current editor state if available
        if (editorRef.current) {
          try {
            const currentSnapshot = editorRef.current.getSnapshot()
            updateData.tldraw_data = currentSnapshot
          } catch (error) {
            console.warn('Could not capture current editor state:', error)
          }
        }

        await manualSave(currentMoodboardId, updateData)

        // Show success message
        toast({
          title: "Mood Board guardado",
          description: "Los cambios han sido guardados exitosamente",
        })
      }
    } catch (error) {
      console.error('Error saving moodboard:', error)

      // Show error message to user
      toast({
        title: "Error al guardar",
        description: error instanceof Error ? error.message : "Ocurrió un error inesperado al guardar el mood board",
        variant: "destructive"
      })
    }
  }



  const handleExport = () => {
    console.log('Main export button clicked!')
    console.log('Global export function:', globalExportFunction)

    if (globalExportFunction) {
      globalExportFunction()
    } else {
      console.log('No global export function available')
      toast({
        title: "Error",
        description: "Función de exportar no disponible",
        variant: "destructive"
      })
    }
  }

  const handleShare = async () => {
    try {
      const shareData = {
        title: boardTitle,
        text: `Mira mi mood board: ${boardTitle}`,
        url: window.location.href
      }

      if (navigator.share) {
        await navigator.share(shareData)
        toast({
          title: "¡Compartido!",
          description: "Tu mood board se ha compartido exitosamente"
        })
      } else {
        // Fallback: copiar URL al clipboard
        await navigator.clipboard.writeText(window.location.href)
        toast({
          title: "URL copiada",
          description: "El enlace se ha copiado al portapapeles"
        })
      }
    } catch (error) {
      toast({
        title: "Error al compartir",
        description: "Hubo un problema al compartir tu mood board",
        variant: "destructive"
      })
    }
  }

  const toggleVisibility = async () => {
    if (!currentMoodboardId) return

    try {
      await manualSave(currentMoodboardId, {
        is_public: !moodboard?.is_public
      })
    } catch (error) {
      console.error('Error updating visibility:', error)
    }
  }

  const startEditingTitle = () => {
    setIsEditingTitle(true)
    setTimeout(() => {
      titleInputRef.current?.focus()
      titleInputRef.current?.select()
    }, 0)
  }

  const saveTitle = async () => {
    if (tempTitle.trim()) {
      setIsEditingTitle(false)

      if (currentMoodboardId) {
        try {
          await manualSave(currentMoodboardId, {
            title: tempTitle.trim()
          })
        } catch (error) {
          console.error('Error updating title:', error)
        }
      }
    } else {
      setIsEditingTitle(false)
    }
  }

  const cancelEditTitle = () => {
    setTempTitle("")
    setIsEditingTitle(false)
  }

  const handleTitleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      saveTitle()
    } else if (e.key === 'Escape') {
      cancelEditTitle()
    }
  }

  const formatLastSaved = () => {
    if (!lastSaved) return "No guardado"
    const now = new Date()
    const diff = now.getTime() - lastSaved.getTime()
    const minutes = Math.floor(diff / 60000)

    if (minutes < 1) return "Guardado hace un momento"
    if (minutes === 1) return "Guardado hace 1 minuto"
    return `Guardado hace ${minutes} minutos`
  }

  // Show loading state while authenticating
  if (isAuthLoading) {
    return (
      <div className="fixed inset-0 bg-white z-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-indigo-600" />
          <p className="text-gray-600">Cargando...</p>
        </div>
      </div>
    )
  }

  // Show authentication required state (following Visual Complexity Analyzer pattern)
  if (!user || user.id === 'anonymous') {
    return (
      <div className="fixed inset-0 bg-white z-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600 mb-4">Debes iniciar sesión para usar el editor de mood boards</p>
          <Button onClick={() => setLocation('/login')} className="bg-gradient-to-r from-indigo-600 to-purple-600">
            Iniciar Sesión
          </Button>
        </div>
      </div>
    )
  }

  // Show loading state while loading moodboard data
  if (boardId !== 'new' && isLoadingMoodboard) {
    return (
      <div className="fixed inset-0 bg-white z-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-indigo-600" />
          <p className="text-gray-600">Cargando mood board...</p>
        </div>
      </div>
    )
  }

  // Show error state if there's an error loading moodboard
  if (moodboardError) {
    return (
      <div className="fixed inset-0 bg-white z-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error al cargar el mood board</p>
          <Button onClick={() => setLocation('/dashboard/herramientas/mood-board')} variant="outline">
            Volver a la lista
          </Button>
        </div>
      </div>
    )
  }

  const currentTitle = moodboard?.title || tempTitle || "Nuevo Mood Board"
  const isPublic = moodboard?.is_public || false

  return (
    <div className="fixed inset-0 bg-white z-50 flex flex-col">
      {/* Top Toolbar */}
      <AnimatePresence>
        {isToolbarVisible && (
          <motion.div
            initial={{ y: -100 }}
            animate={{ y: 0 }}
            exit={{ y: -100 }}
            className="bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between shadow-sm z-10"
          >
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBack}
                className="hover:bg-gray-100"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Volver
              </Button>
              
              <div className="h-6 w-px bg-gray-300" />
              
              <div className="flex items-center gap-3">
                {isEditingTitle ? (
                  <div className="flex items-center gap-2">
                    <Input
                      ref={titleInputRef}
                      value={tempTitle}
                      onChange={(e) => setTempTitle(e.target.value)}
                      onKeyDown={handleTitleKeyPress}
                      className="text-lg font-semibold h-8 min-w-[200px]"
                      placeholder="Nombre del mood board"
                    />
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={saveTitle}
                      className="h-8 w-8 p-0"
                    >
                      <Check className="w-4 h-4 text-green-600" />
                    </Button>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={cancelEditTitle}
                      className="h-8 w-8 p-0"
                    >
                      <X className="w-4 h-4 text-red-600" />
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <h1 className="text-lg font-semibold">{currentTitle}</h1>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={startEditingTitle}
                      className="h-8 w-8 p-0 opacity-60 hover:opacity-100"
                    >
                      <Edit2 className="w-3 h-3" />
                    </Button>
                  </div>
                )}
                <Badge variant={isPublic ? "default" : "secondary"}>
                  {isPublic ? "Público" : "Privado"}
                </Badge>
                {hasUnsavedChanges && (
                  <Badge variant="outline" className="text-orange-600 border-orange-600">
                    Sin guardar
                  </Badge>
                )}
              </div>
              
              <div className="text-sm text-gray-500">
                {formatLastSaved()}
              </div>
            </div>

            <div className="flex items-center gap-2">
              {/* Auto-save indicator */}
              {(isCreating || isUpdating) && (
                <div className="flex items-center gap-2 text-sm text-blue-600">
                  <Loader2 className="w-3 h-3 animate-spin" />
                  {isCreating ? "Creando..." : "Guardando..."}
                </div>
              )}

              <Button
                variant="ghost"
                size="sm"
                onClick={toggleVisibility}
                className="hover:bg-gray-100"
                disabled={!currentMoodboardId}
              >
                {isPublic ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={handleSave}
                className="hover:bg-gray-100"
                disabled={isCreating || isUpdating}
              >
                <Save className="w-4 h-4 mr-2" />
                {boardId === 'new' && !currentMoodboardId ? "Crear" : "Guardar"}
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={handleShare}
                className="hover:bg-gray-100"
              >
                <Share2 className="w-4 h-4 mr-2" />
                Compartir
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={handleExport}
                className="hover:bg-gray-100"
                data-export-btn
              >
                <Download className="w-4 h-4 mr-2" />
                Exportar
              </Button>

              <div className="h-6 w-px bg-gray-300" />

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="hover:bg-gray-100">
                    <Settings className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => setIsToolbarVisible(false)}>
                    Ocultar barra de herramientas
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Users className="w-4 h-4 mr-2" />
                    Colaboradores
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    Configuración del tablero
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Canvas Area */}
      <div className="flex-1 relative">
        <TLUiTranslationProvider locale="es">
          <Tldraw
            persistenceKey={currentMoodboardId ? `emma-mood-board-${currentMoodboardId}` : `emma-mood-board-temp-${boardId}`}
            onMount={(editor) => {
              // Store editor reference for manual saves
              editorRef.current = editor

              // Load existing tldraw data if available
              if (moodboard?.tldraw_data) {
                try {
                  editor.loadSnapshot(moodboard.tldraw_data)
                } catch (error) {
                  console.error('Error loading moodboard data:', error)
                }
              }
            }}
            onChange={(editor) => {
              // Auto-save on changes
              if (currentMoodboardId && enableAutoSave) {
                const snapshot = editor.getSnapshot()
                scheduleAutoSave(currentMoodboardId, snapshot)
              }
            }}
            shareZone={
              <div>
                <TldrawExportHandler boardTitle={currentTitle} toast={toast} />
                {!isToolbarVisible && (
                  <div className="absolute top-4 left-4 z-10">
                    <Button
                      variant="secondary"
                      size="sm"
                    onClick={() => setIsToolbarVisible(true)}
                    className="bg-white/90 backdrop-blur-sm shadow-lg"
                  >
                    Mostrar herramientas
                  </Button>
                </div>
              )}
            </div>
          }
        />
        </TLUiTranslationProvider>
      </div>

      {/* Floating Action Button for Mobile */}
      <div className="fixed bottom-20 right-6 md:hidden">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              size="lg"
              className="w-14 h-14 rounded-full bg-gradient-to-r from-indigo-600 to-purple-600 shadow-lg"
            >
              <Settings className="w-6 h-6" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="mb-2">
            <DropdownMenuItem onClick={handleSave}>
              <Save className="w-4 h-4 mr-2" />
              Guardar
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleShare}>
              <Share2 className="w-4 h-4 mr-2" />
              Compartir
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleExport}>
              <Download className="w-4 h-4 mr-2" />
              Exportar
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleBack}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Keyboard Shortcuts Hint */}
      <div className="fixed bottom-4 left-4 text-xs text-gray-500 bg-white/80 backdrop-blur-sm px-2 py-1 rounded">
        Presiona H para ocultar/mostrar herramientas
      </div>



    </div>
  )
}
