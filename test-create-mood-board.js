/**
 * Test script to create a mood board through the frontend API
 * Run this in the browser console on http://localhost:3002/dashboard/herramientas/mood-board
 */

async function createTestMoodBoard() {
  console.log('🎨 Creating Test Mood Board...\n');
  
  try {
    // Test authentication first
    const { supabase } = await import('/src/lib/supabase.ts');
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.error('❌ User not authenticated');
      return;
    }
    
    console.log('✅ User authenticated:', user.email);
    
    // Create test mood board data
    const testMoodBoard = {
      title: `Test Mood Board ${new Date().toLocaleTimeString()}`,
      description: 'A test mood board created to verify the frontend-backend integration is working correctly.',
      tags: ['test', 'frontend', 'integration', 'demo'],
      tldraw_data: {
        shapes: [
          {
            id: 'shape-1',
            type: 'text',
            props: {
              text: 'Frontend Test ✅',
              color: 'blue',
              size: 'large'
            },
            x: 100,
            y: 100
          },
          {
            id: 'shape-2',
            type: 'text',
            props: {
              text: 'Backend Integration ✅',
              color: 'green',
              size: 'medium'
            },
            x: 100,
            y: 200
          },
          {
            id: 'shape-3',
            type: 'text',
            props: {
              text: 'JWT Authentication ✅',
              color: 'purple',
              size: 'medium'
            },
            x: 100,
            y: 300
          }
        ],
        bindings: [],
        assets: []
      },
      is_public: false,
      collaboration_enabled: false
    };
    
    console.log('📝 Creating mood board with data:', testMoodBoard);
    
    // Make API call to create mood board
    const response = await fetch('http://localhost:8000/api/moodboard/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session.access_token}`
      },
      body: JSON.stringify(testMoodBoard)
    });
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`HTTP ${response.status}: ${errorData.detail || response.statusText}`);
    }
    
    const result = await response.json();
    
    if (result.success) {
      console.log('✅ Mood board created successfully!');
      console.log('📄 Created mood board:', {
        id: result.data?.id,
        title: result.data?.title,
        description: result.data?.description,
        tags: result.data?.tags
      });
      
      // Wait a moment and then refresh the page to see the new mood board
      console.log('🔄 Refreshing page in 2 seconds to show the new mood board...');
      setTimeout(() => {
        window.location.reload();
      }, 2000);
      
      return result.data;
    } else {
      console.error('❌ Failed to create mood board:', result.message);
    }
    
  } catch (error) {
    console.error('❌ Error creating mood board:', error);
  }
}

// Auto-run the test
createTestMoodBoard();
